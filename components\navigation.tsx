"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";

import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

export function Navigation() {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <motion.nav
      className="fixed top-0 w-full bg-white/90 backdrop-blur-md z-50 border-b border-gray-100/50 shadow-sm"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{
        duration: 0.8,
        ease: "easeOut",
        type: "spring",
        stiffness: 100
      }}
    >
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex justify-between items-center h-18">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Link href="/" className="flex items-center space-x-3 group">
              <motion.div
                whileHover={{ rotate: 5 }}
                transition={{ duration: 0.2 }}
              >
                <Image
                  src="/images/wlc-logo.png"
                  alt="WLC Academy"
                  width={36}
                  height={36}
                  className="rounded-lg shadow-sm group-hover:shadow-md transition-shadow duration-200"
                />
              </motion.div>
              <span className="text-xl font-semibold text-gray-900 group-hover:text-[#07243C] transition-colors duration-200">
                WLC Academy
              </span>
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-10">
            <NavigationMenu>
              <NavigationMenuList className="space-x-2">
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <motion.div
                      whileHover={{ y: -1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Link
                        href="/"
                        className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-[#07243C] transition-colors duration-200 rounded-lg hover:bg-gray-50"
                      >
                        Home
                      </Link>
                    </motion.div>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <motion.div
                      whileHover={{ y: -1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Link
                        href="/about"
                        className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-[#07243C] transition-colors duration-200 rounded-lg hover:bg-gray-50"
                      >
                        About
                      </Link>
                    </motion.div>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <motion.div
                      whileHover={{ y: -1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Link
                        href="/programs"
                        className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-[#07243C] transition-colors duration-200 rounded-lg hover:bg-gray-50"
                      >
                        Programs
                      </Link>
                    </motion.div>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <motion.div
                      whileHover={{ y: -1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Link
                        href="/contact"
                        className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-[#07243C] transition-colors duration-200 rounded-lg hover:bg-gray-50"
                      >
                        Contact
                      </Link>
                    </motion.div>
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button className="bg-[#07243C] text-white hover:bg-[#0a2d47] px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 font-medium">
                Enroll Now
              </Button>
            </motion.div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(!isOpen)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <motion.div
                  animate={{ rotate: isOpen ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                </motion.div>
              </Button>
            </motion.div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <motion.div
            className="md:hidden"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="px-2 pt-4 pb-6 space-y-2 sm:px-3 bg-white/95 backdrop-blur-md border-t border-gray-100/50">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Link
                  href="/"
                  className="block px-4 py-3 text-base font-medium text-gray-700 hover:text-[#07243C] hover:bg-gray-50 rounded-lg transition-all duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  Home
                </Link>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Link
                  href="/about"
                  className="block px-4 py-3 text-base font-medium text-gray-700 hover:text-[#07243C] hover:bg-gray-50 rounded-lg transition-all duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  About
                </Link>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Link
                  href="/programs"
                  className="block px-4 py-3 text-base font-medium text-gray-700 hover:text-[#07243C] hover:bg-gray-50 rounded-lg transition-all duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  Programs
                </Link>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Link
                  href="/contact"
                  className="block px-4 py-3 text-base font-medium text-gray-700 hover:text-[#07243C] hover:bg-gray-50 rounded-lg transition-all duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  Contact
                </Link>
              </motion.div>
              <motion.div
                className="px-4 py-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Button className="w-full bg-[#07243C] text-white hover:bg-[#0a2d47] py-3 rounded-lg shadow-md font-medium">
                  Enroll Now
                </Button>
              </motion.div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.nav>
  );
}