"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { Spotlight } from "@/components/ui/spotlight";
import { FloatingElements } from "@/components/ui/floating-elements";

import { ArrowRight, Play } from "lucide-react";

const stats = [
  { number: "500+", label: "Students" },
  { number: "50+", label: "Expert Teachers" },
  { number: "15+", label: "Years Experience" },
  { number: "98%", label: "Success Rate" },
];

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-gray-50 overflow-hidden">
      <Spotlight className="-top-40 left-0 md:left-60 md:-top-20" fill="#F3CC5C" />
      <BackgroundBeams />
      <FloatingElements />

      {/* Enhanced yellow background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large yellow orbs */}
        <motion.div
          className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-br from-[#F3CC5C]/10 to-[#F3CC5C]/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            x: [0, 50, 0],
            y: [0, 30, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute -bottom-40 -right-40 w-96 h-96 bg-gradient-to-tl from-[#F3CC5C]/8 to-[#F3CC5C]/3 rounded-full blur-3xl"
          animate={{
            scale: [1.1, 1, 1.1],
            x: [0, -30, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-[#F3CC5C]/5 via-[#F3CC5C]/2 to-transparent rounded-full blur-2xl"
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "linear",
          }}
        />

        {/* Floating yellow particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-[#F3CC5C]/30 rounded-full"
            style={{
              left: `${10 + i * 8}%`,
              top: `${20 + (i % 3) * 25}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
              scale: [0.5, 1.5, 0.5],
            }}
            transition={{
              duration: 6 + i * 1.2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.8,
            }}
          />
        ))}

        {/* Yellow accent lines */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-[1px] bg-gradient-to-r from-transparent via-[#F3CC5C]/20 to-transparent"
            style={{
              width: `${150 + i * 50}px`,
              top: `${15 + i * 15}%`,
              left: `${-20 + i * 20}%`,
              transform: `rotate(${i * 30}deg)`,
            }}
            animate={{
              opacity: [0, 0.8, 0],
              scaleX: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 1.5,
            }}
          />
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 text-center py-20">
        {/* Hero Badge */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.8,
            delay: 0.2,
            type: "spring",
            stiffness: 120
          }}
          className="mb-8"
        >
          <div className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-[#F3CC5C]/10 via-[#F3CC5C]/5 to-transparent border border-[#F3CC5C]/20 rounded-full backdrop-blur-md shadow-lg">
            <motion.div
              className="relative"
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            >
              <div className="w-3 h-3 bg-[#F3CC5C] rounded-full"></div>
              <motion.div
                className="absolute inset-0 w-3 h-3 bg-[#F3CC5C] rounded-full"
                animate={{ scale: [1, 1.5, 1], opacity: [1, 0, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </motion.div>
            <span className="text-sm font-semibold text-[#07243C] tracking-wide uppercase">
              Premium Afterschool Excellence
            </span>
          </div>
        </motion.div>

        {/* Main Title */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.4 }}
        >
          <motion.h1
            className="text-6xl md:text-7xl lg:text-8xl xl:text-9xl 2xl:text-[10rem] font-black text-transparent bg-clip-text bg-gradient-to-br from-gray-900 via-gray-800 to-gray-600 leading-[0.85] tracking-[-0.025em] mb-6"
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{
              duration: 1.2,
              delay: 0.6,
              type: "spring",
              stiffness: 80
            }}
          >
            <motion.span
              className="block"
              initial={{ x: -50 }}
              animate={{ x: 0 }}
              transition={{ duration: 0.8, delay: 1 }}
            >
              Nurturing
            </motion.span>
            <motion.span
              className="block text-transparent bg-clip-text bg-gradient-to-r from-[#F3CC5C] via-[#F3CC5C] to-[#07243C]"
              initial={{ x: 50 }}
              animate={{ x: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            >
              Growth
            </motion.span>
            <motion.span
              className="block"
              initial={{ y: 50 }}
              animate={{ y: 0 }}
              transition={{ duration: 0.8, delay: 1.4 }}
            >
              After School
            </motion.span>
          </motion.h1>

          {/* Accent elements */}
          <div className="relative">
            <motion.div
              className="absolute -top-8 left-1/2 -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-[#F3CC5C] to-transparent"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 1.5, delay: 2 }}
            />
            <motion.div
              className="absolute -bottom-8 left-1/2 -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-[#07243C] to-transparent"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 1.5, delay: 2.2 }}
            />
          </div>
        </motion.div>

        {/* Description */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 1.0,
            delay: 1.6,
            type: "spring",
            stiffness: 60
          }}
        >
          <p className="text-xl md:text-2xl lg:text-3xl text-gray-600 leading-relaxed max-w-5xl mx-auto font-light">
            A comprehensive <span className="text-[#F3CC5C] font-medium">year-round afterschool program</span> providing
            academic support, enrichment activities, and a safe learning environment for students
            <span className="text-[#07243C] font-medium"> ages 5-14</span>.
          </p>
        </motion.div>

        {/* Program Details */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.8,
            delay: 1.8,
            type: "spring",
            stiffness: 80
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <motion.div
              className="group relative overflow-hidden bg-gradient-to-br from-white/80 to-white/40 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl"
              whileHover={{ y: -5, scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-[#F3CC5C]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="relative z-10">
                <motion.div
                  className="w-4 h-4 bg-[#F3CC5C] rounded-full mb-4 mx-auto"
                  animate={{
                    scale: [1, 1.2, 1],
                    boxShadow: ["0 0 0 0 rgba(243, 204, 92, 0.4)", "0 0 0 10px rgba(243, 204, 92, 0)", "0 0 0 0 rgba(243, 204, 92, 0)"]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Schedule</h3>
                <p className="text-gray-600 font-medium">Monday - Friday</p>
              </div>
            </motion.div>

            <motion.div
              className="group relative overflow-hidden bg-gradient-to-br from-white/80 to-white/40 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl"
              whileHover={{ y: -5, scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-[#07243C]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="relative z-10">
                <motion.div
                  className="w-4 h-4 bg-[#07243C] rounded-full mb-4 mx-auto"
                  animate={{
                    scale: [1, 1.2, 1],
                    boxShadow: ["0 0 0 0 rgba(7, 36, 60, 0.4)", "0 0 0 10px rgba(7, 36, 60, 0)", "0 0 0 0 rgba(7, 36, 60, 0)"]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.7
                  }}
                />
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Hours</h3>
                <p className="text-gray-600 font-medium">3:00 PM - 6:00 PM</p>
              </div>
            </motion.div>

            <motion.div
              className="group relative overflow-hidden bg-gradient-to-br from-white/80 to-white/40 backdrop-blur-lg rounded-2xl p-6 border border-white/20 shadow-xl"
              whileHover={{ y: -5, scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-[#F3CC5C]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="relative z-10">
                <motion.div
                  className="w-4 h-4 bg-[#F3CC5C] rounded-full mb-4 mx-auto"
                  animate={{
                    scale: [1, 1.2, 1],
                    boxShadow: ["0 0 0 0 rgba(243, 204, 92, 0.4)", "0 0 0 10px rgba(243, 204, 92, 0)", "0 0 0 0 rgba(243, 204, 92, 0)"]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1.4
                  }}
                />
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Duration</h3>
                <p className="text-gray-600 font-medium">Year-Round Program</p>
              </div>
            </motion.div>
          </div>
        </motion.div>
        </motion.div>

        {/* CTA Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row items-center justify-center gap-6"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 1.0,
            delay: 2.0,
            type: "spring",
            stiffness: 60
          }}
        >
          <motion.div
            whileHover={{ scale: 1.05, y: -3 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.8,
              delay: 2.2,
              type: "spring",
              stiffness: 100
            }}
          >
            <Button
              size="lg"
              className="relative group bg-gradient-to-r from-[#07243C] to-[#0a2d47] text-white px-10 py-4 text-lg font-semibold rounded-2xl shadow-2xl hover:shadow-[#07243C]/25 transition-all duration-500 border-0 overflow-hidden"
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-[#F3CC5C] to-[#F3CC5C]/80 opacity-0 group-hover:opacity-20 transition-opacity duration-300"
                whileHover={{ scale: 1.1 }}
              />
              <span className="relative z-10 flex items-center gap-3">
                Enroll Today
                <motion.div
                  animate={{ x: [0, 4, 0] }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <ArrowRight className="h-5 w-5" />
                </motion.div>
              </span>
            </Button>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.05, y: -3 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.8,
              delay: 2.4,
              type: "spring",
              stiffness: 100
            }}
          >
            <Button
              variant="outline"
              size="lg"
              className="relative group text-[#07243C] border-2 border-[#07243C]/20 hover:border-[#F3CC5C] hover:bg-[#F3CC5C]/5 px-10 py-4 text-lg font-semibold rounded-2xl backdrop-blur-lg bg-white/60 shadow-xl hover:shadow-2xl transition-all duration-500"
            >
              <span className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{
                    duration: 10,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                >
                  <Play className="h-5 w-5" />
                </motion.div>
                Learn More
              </span>
            </Button>
          </motion.div>
        </motion.div>

        {/* Stats - Commented out for cleaner design */}
        {/* <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto pt-8 border-t border-gray-100"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
              className="text-center"
            >
              <div className="text-2xl md:text-3xl font-medium text-gray-900 mb-1">
                {stat.number}
              </div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div> */}
      </div>


    </section>
  );
}
