"use client";

import React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { Spotlight } from "@/components/ui/spotlight";
import { FloatingElements } from "@/components/ui/floating-elements";

import { ArrowRight, Play } from "lucide-react";

const stats = [
  { number: "500+", label: "Students" },
  { number: "50+", label: "Expert Teachers" },
  { number: "15+", label: "Years Experience" },
  { number: "98%", label: "Success Rate" },
];

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-b from-white via-gray-50/20 to-white overflow-hidden">
      <Spotlight className="-top-40 left-0 md:left-60 md:-top-20" fill="#F3CC5C" />
      <BackgroundBeams />
      <FloatingElements />

      {/* Subtle animated background overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-[#F3CC5C]/[0.02] via-transparent to-[#07243C]/[0.02]"
        animate={{
          opacity: [0.5, 0.8, 0.5],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Animated particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-[#F3CC5C]/30 rounded-full"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + i * 10}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 6 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 1.5,
            }}
          />
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{
              duration: 0.8,
              delay: 0.2,
              type: "spring",
              stiffness: 100
            }}
            className="inline-flex items-center px-6 py-3 bg-[#F3CC5C]/10 border border-[#F3CC5C]/20 rounded-full text-sm font-medium text-[#07243C] mb-16 backdrop-blur-sm"
          >
            <motion.span
              className="w-2 h-2 bg-[#F3CC5C] rounded-full mr-3"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.8, 1, 0.8]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            Excellence in Afterschool Education
          </motion.div>

          <motion.div
            className="text-7xl md:text-8xl lg:text-9xl xl:text-[10rem] font-extralight text-gray-900 mb-12 leading-[0.85] tracking-tighter"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 1.2,
              delay: 0.4,
              type: "spring",
              stiffness: 80
            }}
          >
            <TextGenerateEffect
              words="Nurturing Growth After School"
              className="text-7xl md:text-8xl lg:text-9xl xl:text-[10rem] font-extralight text-gray-900 leading-[0.85] tracking-tighter"
            />
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 1.0,
              delay: 0.8,
              type: "spring",
              stiffness: 60
            }}
            className="text-xl md:text-2xl lg:text-3xl text-gray-500 leading-relaxed max-w-5xl mx-auto mb-6 font-light"
          >
            A comprehensive year-round afterschool program providing academic support,
            enrichment activities, and a safe learning environment for students ages 5-14.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.8,
              delay: 1.2,
              type: "spring",
              stiffness: 80
            }}
            className="text-lg text-gray-400 max-w-3xl mx-auto mb-4"
          >
            <motion.span
              animate={{
                color: ["#9CA3AF", "#F3CC5C", "#9CA3AF"]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              Monday - Friday
            </motion.span>
            {" • "}
            <motion.span
              animate={{
                color: ["#9CA3AF", "#07243C", "#9CA3AF"]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1.3
              }}
            >
              3:00 PM - 6:00 PM
            </motion.span>
            {" • "}
            <motion.span
              animate={{
                color: ["#9CA3AF", "#F3CC5C", "#9CA3AF"]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2.6
              }}
            >
              Year-Round Program
            </motion.span>
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 1.0,
            delay: 1.6,
            type: "spring",
            stiffness: 60
          }}
          className="flex flex-col sm:flex-row items-center justify-center gap-8 mb-24"
        >
          <motion.div
            whileHover={{
              scale: 1.05,
              y: -2
            }}
            whileTap={{ scale: 0.95 }}
            className="group"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.8,
              delay: 1.8,
              type: "spring",
              stiffness: 100
            }}
          >
            <Button
              size="lg"
              className="bg-[#07243C] text-white hover:bg-[#0a2d47] px-12 py-5 text-xl font-medium group relative overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-500 rounded-xl"
            >
              <motion.span
                className="relative z-10 flex items-center"
                whileHover={{ x: 2 }}
                transition={{ duration: 0.2 }}
              >
                Enroll Today
                <motion.div
                  animate={{ x: [0, 3, 0] }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <ArrowRight className="ml-3 h-6 w-6" />
                </motion.div>
              </motion.span>
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-[#F3CC5C] to-[#07243C] opacity-0 group-hover:opacity-20 transition-opacity duration-500"
                whileHover={{ scale: 1.1 }}
              />
            </Button>
          </motion.div>

          <motion.div
            whileHover={{
              scale: 1.05,
              y: -2
            }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.8,
              delay: 2.0,
              type: "spring",
              stiffness: 100
            }}
          >
            <Button
              variant="outline"
              size="lg"
              className="text-[#07243C] border-[#07243C]/30 hover:bg-[#07243C]/10 hover:border-[#07243C] px-12 py-5 text-xl font-medium group backdrop-blur-sm bg-white/60 rounded-xl shadow-lg hover:shadow-xl transition-all duration-500"
            >
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "linear"
                }}
              >
                <Play className="mr-3 h-6 w-6" />
              </motion.div>
              Learn More
            </Button>
          </motion.div>
        </motion.div>

        {/* Stats - Commented out for cleaner design */}
        {/* <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto pt-8 border-t border-gray-100"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.4 + index * 0.1 }}
              className="text-center"
            >
              <div className="text-2xl md:text-3xl font-medium text-gray-900 mb-1">
                {stat.number}
              </div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div> */}
      </div>


    </section>
  );
}
